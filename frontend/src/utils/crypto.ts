// frontend/src/utils/crypto.ts
import sodium from "libsodium-wrappers";

// Ensure sodium is ready before using any crypto functions
let sodiumReady = false;
const initSodium = async () => {
  if (!sodiumReady) {
    await sodium.ready;
    sodiumReady = true;
  }
};

export interface SodiumKeyPair {
  publicKey: Uint8Array;
  privateKey: Uint8Array;
}

export interface SessionKeys {
  sharedTx: Uint8Array;
  sharedRx: Uint8Array;
}

function extractRawX25519FromSpki(spki: Uint8Array): Uint8Array {
  if (spki.length === 32) {
    // Already raw
    return spki;
  }
  if (spki.length > 32) {
    return spki.slice(spki.length - 32);
  }
  throw new Error(`Invalid key length: ${spki.length}`);
}

export class CryptoUtils {
  /**
   * Generate a new X25519 key pair using libsodium
   */
  static async generateKeyPair(): Promise<SodiumKeyPair> {
    await initSodium();
    console.debug("🔐 Generating X25519 key pair with libsodium...");

    const keyPair = sodium.crypto_kx_keypair();
    console.debug("🔐 ✅ X25519 key pair generated successfully");

    return {
      publicKey: keyPair.publicKey,
      privateKey: keyPair.privateKey,
    };
  }

  /**
   * Generate a signing key pair using Ed25519 with libsodium
   */
  static async generateSigningKeyPair(): Promise<SodiumKeyPair> {
    await initSodium();
    console.debug("🔐 Generating Ed25519 signing key pair with libsodium...");

    const keyPair = sodium.crypto_sign_keypair();
    console.debug("🔐 ✅ Ed25519 signing key pair generated successfully");

    return {
      publicKey: keyPair.publicKey,
      privateKey: keyPair.privateKey,
    };
  }

  /**
   * Export public key to base64 string
   */
  static async exportPublicKey(key: Uint8Array): Promise<string> {
    await initSodium();
    return sodium.to_base64(key);
  }

  /**
   * Export private key to base64 string
   */
  static async exportPrivateKey(key: Uint8Array): Promise<string> {
    await initSodium();
    return sodium.to_base64(key);
  }

  /**
   * Import public key from base64 string
   */
  // static async importPublicKey(keyData: string, algorithm?: string): Promise<Uint8Array> {
  //   await initSodium();
  //   console.debug(`🔐 Importing public key (libsodium)`);
  //   console.debug(`🔐 Key data length: ${keyData.length} and key: ${keyData}`);

  //   try {
  //     const binaryKey = sodium.from_base64(keyData);
  //     console.debug(`🔐 ✅ Public key imported successfully`);
  //     return binaryKey;
  //   } catch (error) {
  //     console.error(`🔐 ❌ Failed to import public key:`, error);
  //     throw new Error(`Failed to import public key: ${error instanceof Error ? error.message : 'Unknown error'}`);
  //   }
  // }

  static async importPublicKey(keyData: string): Promise<Uint8Array> {
    await initSodium();
    console.debug(`🔐 Importing public key (libsodium)`);

    try {
      let spkiBytes;
      try {
        spkiBytes = sodium.from_base64(
          keyData,
          sodium.base64_variants.ORIGINAL
        );
      } catch {
        spkiBytes = sodium.from_base64(keyData, sodium.base64_variants.URLSAFE);
      }

      const rawKey = extractRawX25519FromSpki(spkiBytes);

      console.debug(
        `🔐 ✅ Public key imported successfully (length: ${rawKey.length})`
      );
      return rawKey;
    } catch (error) {
      console.error(`🔐 ❌ Failed to import public key:`, error);
      throw new Error(
        `Failed to import public key: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Import private key from base64 string
   */
  // static async importPrivateKey(
  //   keyData: string,
  //   algorithm?: string
  // ): Promise<Uint8Array> {
  //   await initSodium();
  //   console.debug(`🔐 Importing private key (libsodium)`);

  //   try {
  //     const binaryKey = sodium.from_base64(keyData);
  //     console.debug(`🔐 ✅ Private key imported successfully`);
  //     return binaryKey;
  //   } catch (error) {
  //     console.error(`🔐 ❌ Failed to import private key:`, error);
  //     throw new Error(
  //       `Failed to import private key: ${
  //         error instanceof Error ? error.message : "Unknown error"
  //       }`
  //     );
  //   }
  // }

  static async importPrivateKey(keyData: string): Promise<Uint8Array> {
    await initSodium();
    console.debug(`🔐 Importing private key (libsodium)`);

    try {
      const binaryKey = sodium.from_base64(keyData);
      console.debug(`🔐 ✅ Private key imported successfully`);
      return binaryKey;
    } catch (error) {
      console.error(`🔐 ❌ Failed to import private key:`, error);
      throw new Error(
        `Failed to import private key: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Derive session keys using libsodium crypto_kx (client side)
   */
  static async deriveClientSessionKeys(
    clientPublicKey: Uint8Array,
    clientPrivateKey: Uint8Array,
    serverPublicKey: Uint8Array
  ): Promise<SessionKeys> {
    await initSodium();
    console.debug("🔐 Deriving client session keys...");

    const sessionKeys = sodium.crypto_kx_client_session_keys(
      clientPublicKey,
      clientPrivateKey,
      serverPublicKey
    );

    console.debug("🔐 ✅ Client session keys derived");
    return {
      sharedTx: sessionKeys.sharedTx,
      sharedRx: sessionKeys.sharedRx,
    };
  }

  /**
   * Derive session keys using libsodium crypto_kx (server side)
   */
  static async deriveServerSessionKeys(
    serverPublicKey: Uint8Array,
    serverPrivateKey: Uint8Array,
    clientPublicKey: Uint8Array
  ): Promise<SessionKeys> {
    await initSodium();
    console.debug("🔐 Deriving server session keys...");

    const sessionKeys = sodium.crypto_kx_server_session_keys(
      serverPublicKey,
      serverPrivateKey,
      clientPublicKey
    );

    console.debug("🔐 ✅ Server session keys derived");
    return {
      sharedTx: sessionKeys.sharedTx,
      sharedRx: sessionKeys.sharedRx,
    };
  }

  /**
   * Encrypt message content using libsodium secretbox
   */
  static async encryptMessage(
    content: string,
    key: Uint8Array
  ): Promise<{
    encryptedData: string;
    iv: string;
  }> {
    await initSodium();
    console.debug("🔐 Encrypting message with libsodium...");

    const nonce = sodium.randombytes_buf(sodium.crypto_secretbox_NONCEBYTES);
    const message = sodium.from_string(content);

    const ciphertext = sodium.crypto_secretbox_easy(message, nonce, key);

    console.debug("🔐 ✅ Message encrypted successfully");
    return {
      encryptedData: sodium.to_base64(ciphertext),
      iv: sodium.to_base64(nonce),
    };
  }

  /**
   * Decrypt message content using libsodium secretbox
   */
  static async decryptMessage(
    encryptedData: string,
    iv: string,
    key: Uint8Array
  ): Promise<string> {
    await initSodium();
    console.debug("🔐 Decrypting message with libsodium...");

    try {
      const ciphertext = sodium.from_base64(encryptedData);
      const nonce = sodium.from_base64(iv);

      const decrypted = sodium.crypto_secretbox_open_easy(
        ciphertext,
        nonce,
        key
      );
      const message = sodium.to_string(decrypted);

      console.debug("🔐 ✅ Message decrypted successfully");
      return message;
    } catch (error) {
      console.error("🔐 ❌ Failed to decrypt message:", error);
      throw new Error(
        `Failed to decrypt message: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  /**
   * Sign data using Ed25519 with libsodium
   */
  static async signData(
    data: string | Uint8Array,
    privateKey: Uint8Array
  ): Promise<string> {
    await initSodium();
    console.debug("🔐 Signing data with Ed25519...");

    // Convert string to Uint8Array if needed
    let dataToSign: Uint8Array;
    if (typeof data === "string") {
      dataToSign = sodium.from_string(data);
    } else {
      dataToSign = data;
    }

    const signature = sodium.crypto_sign_detached(dataToSign, privateKey);

    console.debug("🔐 ✅ Data signed successfully");
    return sodium.to_base64(signature);
  }

  /**
   * Verify signature using Ed25519 with libsodium
   */
  static async verifySignature(
    data: string | Uint8Array,
    signature: string,
    publicKey: Uint8Array
  ): Promise<boolean> {
    await initSodium();
    console.debug("🔐 Verifying signature with Ed25519...");

    try {
      const dataToVerify =
        typeof data === "string" ? sodium.from_string(data) : data;
      const signatureBuffer = sodium.from_base64(signature);

      const isValid = sodium.crypto_sign_verify_detached(
        signatureBuffer,
        dataToVerify,
        publicKey
      );
      console.debug(
        `🔐 ✅ Signature verification: ${isValid ? "valid" : "invalid"}`
      );
      return isValid;
    } catch (error) {
      console.error("🔐 ❌ Signature verification failed:", error);
      return false;
    }
  }

  /**
   * Generate random bytes using libsodium
   */
  static async generateRandomBytes(length: number): Promise<Uint8Array> {
    await initSodium();
    return sodium.randombytes_buf(length);
  }

  /**
   * Get the algorithm name (always X25519 for key exchange, Ed25519 for signing)
   */
  static getKeyAlgorithm(key: Uint8Array): string {
    // Since we're using libsodium, we know the algorithms
    return key.length === 32 ? "X25519" : "Ed25519";
  }

  /**
   * Derive a key from input material using libsodium
   */
  static async deriveKey(
    inputMaterial: Uint8Array,
    info: string
  ): Promise<Uint8Array> {
    await initSodium();
    console.debug("🔐 Deriving key with libsodium...");

    // Use crypto_kdf_derive_from_key for key derivation
    const subkeyId = 0;
    const context = new Uint8Array(8);
    const infoBytes = sodium.from_string(info);
    context.set(infoBytes.slice(0, Math.min(8, infoBytes.length)));

    // Use the input material as the master key (pad or truncate to 32 bytes)
    let masterKey = new Uint8Array(32);
    if (inputMaterial.length >= 32) {
      masterKey.set(inputMaterial.slice(0, 32));
    } else {
      masterKey.set(inputMaterial);
    }

    const derivedKey = sodium.crypto_kdf_derive_from_key(
      32,
      subkeyId,
      sodium.to_string(context),
      masterKey
    );
    console.debug("🔐 ✅ Key derived successfully");
    return derivedKey;
  }

  /**
   * Check libsodium support (always true since we're using libsodium-wrappers)
   */
  static async checkCryptoSupport(): Promise<{
    supportsX25519: boolean;
    supportsEd25519: boolean;
    supportsECDH: boolean;
    supportsECDSA: boolean;
  }> {
    await initSodium();
    return {
      supportsX25519: true,
      supportsEd25519: true,
      supportsECDH: false, // We're using X25519 instead
      supportsECDSA: false, // We're using Ed25519 instead
    };
  }

  /**
   * Hash data using SHA-256 with libsodium
   */
  static async hash(data: string | Uint8Array): Promise<string> {
    await initSodium();
    const dataToHash =
      typeof data === "string" ? sodium.from_string(data) : data;
    const hashBuffer = sodium.crypto_hash(dataToHash);
    return sodium.to_base64(hashBuffer);
  }

  /**
   * Derive key from password using libsodium
   */
  static async deriveKeyFromPassword(
    password: string,
    salt: Uint8Array,
    iterations: number = 100000
  ): Promise<Uint8Array> {
    await initSodium();
    const passwordBytes = sodium.from_string(password);

    // Use crypto_pwhash for password-based key derivation
    const key = sodium.crypto_pwhash(
      32, // key length
      passwordBytes,
      salt,
      iterations,
      sodium.crypto_pwhash_MEMLIMIT_INTERACTIVE,
      sodium.crypto_pwhash_ALG_ARGON2ID13
    );

    return key;
  }

  /**
   * Combine multiple shared secrets
   */
  static async combineSharedSecrets(
    secrets: Uint8Array[],
    salt?: Uint8Array
  ): Promise<Uint8Array> {
    await initSodium();

    // Concatenate all secrets
    const totalLength = secrets.reduce((acc, secret) => acc + secret.length, 0);
    const combined = new Uint8Array(totalLength);
    let offset = 0;

    for (const secret of secrets) {
      combined.set(secret, offset);
      offset += secret.length;
    }

    // Use crypto_generichash to derive a proper key from the combined secrets
    const derivedKey = sodium.crypto_generichash(32, combined, salt);
    return derivedKey;
  }

  /**
   * Constant-time comparison of two arrays using libsodium
   */
  static constantTimeEqual(a: Uint8Array, b: Uint8Array): boolean {
    if (a.length !== b.length) {
      return false;
    }

    return sodium.memcmp(a, b);
  }
}

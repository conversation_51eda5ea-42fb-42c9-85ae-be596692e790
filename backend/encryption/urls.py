# backend/encryption/urls.py
from django.urls import path
from . import views

app_name = 'encryption'

urlpatterns = [
    # Key bundle management
    path('key-bundle/upload/', views.upload_key_bundle, name='upload_key_bundle'),
    path('key-bundle/<uuid:user_id>/', views.get_key_bundle, name='get_key_bundle'),
    
    # One-time pre-key management
    path('prekeys/upload/', views.upload_one_time_prekeys, name='upload_one_time_prekeys'),
    path('prekeys/count/', views.get_prekey_count, name='get_prekey_count'),
    
    # Key rotation
    path('keys/rotate/', views.rotate_keys, name='rotate_keys'),
    
    # Session management
    path('sessions/create/', views.create_conversation_session, name='create_conversation_session'),
    path('sessions/', views.get_user_sessions, name='get_user_sessions'),
    path('sessions/<uuid:conversation_id>/', views.get_conversation_session, name='get_conversation_session'),
    path('sessions/<uuid:conversation_id>/update/', views.update_conversation_session, name='update_conversation_session'),
]
